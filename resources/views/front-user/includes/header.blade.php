<!-- Below is the link that users can use to open Preferences Center to change their preferences. Do not modify the ID parameter. Place it where appropriate, style it as needed. -->

<!-- <a href="#" id="open_preferences_center">Update cookies preferences</a> -->
<div id="app">
    <header class="site-header rink-header" @if (Auth::check()) style="display: none;" @endif>
        <div class="container">
            <div class="d-flex align-items-center">
                <div class="header-logo" style="flex: 0.1 0 auto; width: auto;">
                    <a href="{{ url('/') }}"><img src="{{ asset('/assets/front-end/images/logo.png') }}"
                            alt="Clickitfame"></a>
                </div>
                <div class="mobile-lan">
                    <li class="leng"><a href="{{ url('/lang/en') }}"
                            class="{{ App::getLocale() == 'en' ? 'active' : '' }}">EN</a>/<a
                            href="{{ url('/lang/de') }}" class="{{ App::getLocale() == 'de' ? 'active' : '' }}">DE</a>
                    </li>
                </div>
                <div class="h-menu-outer">
                    <ul class="h-menu  mx-auto" id="scrolling-menu">

                        <li class="{{ url('/home') == url()->current() ? 'active' : '' }}">
                            <a href="@if (Request::is('/') || Request::is('/home')) #home
                                @else
                                {{ url('/') }}#home @endif"
                                class="scrollmenu">{{ __('header.home') }}</a>
                        </li>
                        <li class="{{ url('/home') == url()->current() ? 'active' : '' }}">

                            <a href="@if (Request::is('/') || Request::is('/home')) #HowItWorks
                            @else
                            {{ url('/') }}#HowItWorks @endif"
                                class="scrollmenu">{{ __('header.how_it_works') }}</a>
                        </li>

                        <li class="{{ url('/home') == url()->current() ? 'active' : '' }}">
                            <a href=" @if (Request::is('/') || Request::is('/home')) #OurProcess
                            @else
                            {{ url('/') }}#OurProcess @endif"
                                class="scrollmenu">{{ __('header.our_process') }}</a>
                        </li>

                        <li class="{{ url('/home') == url()->current() ? 'active' : '' }}">
                            <a href="@if (Request::is('/') || Request::is('/home')) #FAQ
                            @else
                            {{ url('/') }}#FAQ @endif"
                                class="scrollmenu">{{ __('header.faq') }}</a>
                        </li>
                    </ul>
                    <ul class="h-menu" id="hsj-menu">

                        @if (!Auth::check())
                            <li class="leng">
                                <a href="{{ url('/lang/en') }}"
                                    class="{{ App::getLocale() == 'en' ? 'active' : '' }}">EN</a>/<a
                                    href="{{ url('lang/de') }}"
                                    class="{{ App::getLocale() == 'de' ? 'active' : '' }}">DE</a>
                            </li>
                            <li class="{{ url('login-en') == url()->current() ? 'active' : '' }}"><a
                                    href="{{ url('login') }}">{{ __('header.login') }}</a></li>
                            <li class=""><a href="javascript:void(0)" data-bs-toggle="modal"
                                    data-bs-target="#applicationform">{{ __('header.apply_brand') }}</a></li>
                            <li class=""><a href="javascript:void(0)" data-bs-toggle="modal"
                                    data-bs-target="#applicationform2">{{ __('header.apply_influencer') }}</a></li>
                        @endif
                    </ul>
                </div>
                @if (Auth::check())
                    <span class="showUser">
                        @if (Auth::user()->user_type == 'customer') Brand
                        @else
                            Influencer @endif
                    </span>
                @endif
                <ul class="h-menu" id="hsj-menu">

                    <li class="ntfMenu display-none {{ Request::is('home') || Request::is('/') ? 'active' : '' }}"
                        @if (Auth::check() && Auth::user()->user_type == 'influencer') @if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish')  @else disabled @endif
                        @endif>
                        <a href="{{ url('/') }}" class="icon"><img
                                src="{{ asset('/assets/front-end/images/icons/sidemenu-home.svg') }}" alt="">
                        </a>
                    </li>

                    {{-- @if (Auth::check())
                            @php
                            $notificationCount = DB::table('notifications')->where('notifiable_id', Auth::user()->id)->where('read_at', NULL)->count();
                            @endphp
                            <li class="ntfMenu {{url('notifications')==url()->current()?'active':''}} ">
                                <a href="{{url('notifications')}}" class="icon"><img src="{{ asset('/assets/front-end/images/icons/ball_icon.svg') }}" alt="">
                                    <span class="count">{{$notificationCount}}</span>
                                </a>
                            </li>
                        @endif --}}
                    <div class="d-show d-md-none">
                        <div class="menuToggle menustyle" onclick="openNav()">
                            <img src="{{ asset('/assets/front-end/images/icons/gg_menu-right.svg') }}" alt="">
                        </div>
                        <span href="javascript:void(0)" class="menustyle closebtn" onclick="closeNav()"><i
                                class="fa-solid fa-xmark"></i></span>
                    </div>

                    @if (Auth::check())
                        <li class="drop-state">
                            <!-- <a href="{{ url('/') }}">HOME</a> -->
                            <div class="dropdown">
                                <button type="button" class="user-btn dropdown-toggle" data-bs-toggle="dropdown">
                                    @php
                                        $row = App\Models\SocialConnect::where('user_id', Auth::user()->id)
                                            ->orderBy('followers', 'desc')
                                            ->first();
                                        if (
                                            $row != null &&
                                            $row->count() > 0 &&
                                            $row->picture != '' &&
                                            $row->picture != null
                                        ) {
                                            $src = \Illuminate\Support\Facades\Storage::url($row->picture);
                                        } else {
                                            $src = URL::asset('/assets/front-end/images/icons/default-profile-pic.png');
                                        }
                                    @endphp
                                    <div class="img-usr">
                                        <img src="{{ $src }}" alt="">
                                    </div>
                                    <span>{{ Auth::user()->first_name }}</span>
                                </button>
                                <div class="dropdown-menu dropdown-menu-end">
                                    <a class="dropdown-item" href="{{ url('logout') }}">Log Out</a>
                                </div>
                            </div>
                        </li>
                    @endif
                </ul>
            </div>
        </div>
        <!--modal-->
        <!--end-->
    </header>
    @include('front-user.pages.popup-registration-en')
    <script type="text/javascript">
        $(".closebtn").hide()
        if ($(window).width() < 768) {
            function openNav() {
                $(".h-menu-outer").addClass("openMenu")
                $(".closebtn").show()
                $(".menuToggle").hide()
                // document.getElementById("mySidebar").style.width = "250px";
                // document.getElementById("main").style.marginLeft = "250px";
            }

            function closeNav() {
                $(".h-menu-outer").removeClass("openMenu")
                $(".closebtn").hide()
                $(".menuToggle").show()
                // document.getElementById("mySidebar").style.width = "0";
                // document.getElementById("main").style.marginLeft= "0";
            }
        } else {

        }
    </script>
    <script type="text/javascript" src="{{ URL::asset('/assets/front-end/js/parsley.min.js') }}"></script>

    <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC2oRAljHGZArBeQc5OXY0MI5BBoQproWY&amp;libraries=places">
    </script>

    <script>
        google.maps.event.addDomListener(window, 'load', initialize);

        function initialize() {
            var input = document.getElementById('address');
            var autocomplete = new google.maps.places.Autocomplete(input);
            autocomplete.addListener('place_changed', function() {
                var place = autocomplete.getPlace();
                // place variable will have all the information you are looking for.
                $("#latitude").val(place.geometry['location'].lat());
                $("#longitude").val(place.geometry['location'].lng());
                console.log(place.geometry['location'].lat());
                console.log(place.geometry['location'].lng());

                const geocoder = new google.maps.Geocoder;

            });
        }
    </script>

    <style type="text/css">
        .pac-container {
            z-index: 10000 !important;
        }
    </style>
