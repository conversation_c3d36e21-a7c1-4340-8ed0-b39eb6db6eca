@extends('front-user.layouts.master_user_dashboard')

@section('content')
    <div class="steps-section">
        <div class="steps-point">
            <div class="steps-icon">
                <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-1.png') }}" class="step-icon" alt="">
                <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
            </div>
            <div class="steps-cont"></div>
            <div class="steps-which">Social Media</div>
            <div class="steps-position"></div>
        </div>
        <div class="steps-point">
            <div class="steps-icon">
                <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-2.png') }}" class="step-icon" alt="">
                <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
            </div>
            <div class="steps-cont"></div>
            <div class="steps-which">Influencer Tasks</div>
            <div class="steps-position"></div>
        </div>
        <div class="steps-point">
            <div class="steps-icon">
                <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-4-new.svg') }}" class="step-icon" alt="">
                <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
            </div>
            <div class="steps-cont"></div>
            <div class="steps-which">Campaign details</div>
            <div class="steps-position"></div>
        </div>
        <div class="steps-point">
            <div class="steps-icon">
                <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-3-new.svg') }}" class="step-icon" alt="">
                <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
            </div>
            <div class="steps-cont"></div>
            <div class="steps-which">Marketplace</div>
            <div class="steps-position"></div>
        </div>
        <div class="steps-point">
            <div class="steps-icon">
                <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-5.png') }}" class="step-icon" alt="">
                <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
            </div>
            <div class="steps-cont"></div>
            <div class="steps-which">Payment details</div>
            <div class="steps-position"></div>
        </div>
    </div>
    <form data-parsley-validate id="form1" enctype="multipart/form-data" method="post" >
        @csrf
        <div class="marketplace-imports campaign-type d-block">
            @include('front-user.pages.create-campaign-step-one') 
        </div>
        <div class="marketplace-imports boost-me d-none">
            @include('front-user.pages.create-campaign-step-two')
        </div>
        <div class="marketplace-imports shoutout d-none">
            @include('front-user.pages.create-campaign-step-three')
        </div>
    </form>
@endsection

@section('script_links')
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.css" rel="stylesheet" type="text/css" />
@endsection

@section('script_codes')
<script>
    var mp_step = $('.steps-section .steps-point')
    
    var iEra, thumbLi = $('.steps-section .steps-point');
    for (iEra = 0; iEra <= mp_step.length; iEra++) {
        thumbLi.eq(iEra).attr("id", 'steps-point' + iEra);
    }

    var new_steps = $('.this-steps .new_steps')
    
    var new_step_li, new_step_thumbLi = $('.this-steps .new_steps');
    for (new_step_li = 0; new_step_li <= new_steps.length; new_step_li++) {
        new_step_thumbLi.eq(new_step_li).attr("id", 'new_steps' + new_step_li);
        new_step_thumbLi.eq(new_step_li).find(".mobile-step-detail .steps-cont").text("STEP 0"+ (new_step_li + 1));
    }

    $('input[name="type_post_content"]').click(function(){
        $(this).closest(".marketplace-imports").removeClass("d-block").addClass("d-none")
        if($('input[name="type_post_content"]:checked').val() == "Boost me"){
            $('.nav-left').attr("data-from-step", "boost-me")
            $(this).closest("form").find(".boost-me").removeClass("d-none").addClass("d-block")
            $(".alert-select-option").addClass("d-none")
        }else if($('input[name="type_post_content"]:checked').val() == "Reaction video"){
            $('.nav-left').attr("data-from-step", "Reaction video")
            $(this).closest("form").find(".shoutout").removeClass("d-none").addClass("d-block")
            $(".get_type").hide();
            $(".get_type.reaction-video").show();
            $("#steps-point0").addClass("inprogress");
            $("#new_steps0").show();
        }else if($('input[name="type_post_content"]:checked').val() == "Survey"){
            $('.nav-left').attr("data-from-step", "Survey")
            $(this).closest("form").find(".shoutout").removeClass("d-none").addClass("d-block")
            $(".get_type").hide();
            $(".get_type.survey").show();
            $("#steps-point0").addClass("inprogress");
            $("#new_steps0").show();
        }
    })
</script>
@endsection